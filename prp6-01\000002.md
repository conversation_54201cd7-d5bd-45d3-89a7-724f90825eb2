

**项目多维度代码审查与修复执行指令**

对项目执行全方位的多维度代码审查和修复，确保代码质量达到发布标准。基于以下三个维度进行系统性分析：

**1. 空间思维审查（架构完整性）**
- **模块依赖关系验证**：
  - 检查src/目录下所有.ts文件的import语句准确性
  - 验证out/目录编译输出与src/源码的完整对应关系
  - 确认package.json中的main字段指向正确的编译后入口文件
  - 检查webviewProvider.ts中的资源引用路径正确性
- **配置文件一致性**：
  - 验证package.json与production-config.json中commands、views、viewsContainers配置的完全匹配
  - 确保tsconfig.json的outDir、rootDir与实际目录结构一致
  - 检查.vscode/tasks.json中的npm脚本与package.json中scripts的对应关系
- **文件组织结构**：
  - 验证src/、out/、resources/目录结构符合VSCode扩展规范
  - 检查编译后文件的权限设置（0o644）和目录权限（0o755）
  - 确认.vscodeignore文件正确排除开发文件
- **横切关注点统一性**：
  - 统一所有模块中的错误处理模式（try-catch-log-throw）
  - 确保Logger类在所有模块中的使用方式一致
  - 验证配置读取（vscode.workspace.getConfiguration）的统一性

**2. 立体思维审查（功能完整性）**
- **完整调用链验证**：
  - VSCode命令面板 → registerCommand → MachineIdManager方法调用链
  - Webview UI按钮点击 → postMessage → handleMessage → 后端操作的完整流程
  - 文件系统操作 → 错误处理 → 用户通知的反馈链路
- **API接口一致性**：
  - 验证package.json中定义的命令ID与extension.ts中registerCommand的完全匹配
  - 检查webview中vscode.postMessage的command参数与handleWebviewMessage中case分支的对应
  - 确认webview视图ID在package.json和registerWebviewViewProvider中的一致性
- **错误传播机制**：
  - 文件系统错误（ENOENT、EACCES、EPERM）的正确捕获和用户友好提示
  - JSON解析错误的处理和备份文件完整性验证
  - 网络错误、权限错误的分层处理和错误码映射
- **跨平台兼容性**：
  - Windows: %APPDATA%、%LOCALAPPDATA%路径处理
  - macOS: ~/Library/Application Support路径处理
  - Linux: /etc、/var/lib、~/.config路径处理
  - 文件路径分隔符的正确使用（path.join而非硬编码）
- **资源管理完整性**：
  - fs.promises异步操作的正确使用，避免同步阻塞
  - 文件句柄的及时关闭和错误情况下的资源清理
  - 内存中日志条目的数量限制和定期清理

**3. 逆向思维审查（逻辑一致性）**
- **从用户操作反推**：
  - "重置机器码"操作：备份→生成→写入→验证→通知的完整流程验证
  - "从备份恢复"操作：选择→验证→确认→应用→刷新的逻辑完整性
  - "查看当前ID"操作：读取→掩码→显示→复制功能的正确实现
- **从错误场景反推**：
  - 机器码文件不存在时的自动生成逻辑
  - 备份目录无写权限时的降级处理策略
  - 备份文件损坏时的错误提示和恢复建议
  - 网络断开、磁盘满等极端情况的处理
- **从安全角度反推**：
  - 机器码在日志中的掩码显示（前4位+***+后4位）
  - 备份文件的权限设置（仅当前用户可读写）
  - 敏感信息不被意外写入日志或错误消息
- **从性能角度反推**：
  - 同步文件操作（fs.readFileSync）替换为异步操作
  - 重复的配置读取优化为缓存机制
  - 大量备份文件的并行处理优化

**检查范围**：
- **单文件级别**：
  - TypeScript严格模式下的类型安全检查
  - ESLint规则违规和代码风格一致性
  - 未使用的导入、变量、参数的清理
  - 函数签名的完整性和返回类型声明
- **多文件交叉**：
  - extension.ts与其他模块间的依赖关系
  - webviewProvider.ts中HTML模板与事件处理的匹配
  - machineIdManager.ts与logger.ts的协作正确性
  - 配置文件间的版本号、命令名称一致性
- **项目整体**：
  - VSCode扩展生命周期（activate/deactivate）的正确实现
  - 扩展打包（.vsix）的完整性和安全性
  - 多语言支持的国际化准备
  - 扩展市场发布的合规性检查

**修复要求**：
1. **移除所有模拟数据**：
   - test-webview.html中的模拟机器码数据替换为真实API调用
   - 删除硬编码的示例路径和假数据生成逻辑
   - 确保所有演示代码使用真实的系统调用
2. **完善数据获取**：
   - 机器码读取必须来源于实际的系统文件或硬件标识
   - 备份数据必须基于真实的文件内容和元数据
   - 系统信息获取使用Node.js原生API（os.platform、os.arch等）
3. **修复配置路径**：
   - 所有文件路径使用path.join进行跨平台兼容处理
   - 环境变量的正确读取和默认值设置
   - 相对路径转换为绝对路径的正确处理
4. **完善错误处理**：
   - 每个async函数都包含完整的try-catch块
   - 文件操作前的权限检查（fs.access）
   - JSON.parse操作的异常捕获和格式验证
   - 用户友好的错误消息和操作建议
5. **确保数据一致性**：
   - webview与扩展间的消息协议标准化
   - 错误响应的统一格式（command: 'error', message: string）
   - 状态同步的实时性和准确性保证

**验证标准**：
- 所有机器码操作基于真实文件系统，通过fs.promises API实现
- 代码通过TypeScript严格模式编译，无any类型使用
- 错误处理覆盖率达到100%，所有异步操作都有异常处理
- 跨平台测试通过，支持Windows 10+、macOS 10.15+、Ubuntu 18.04+
- VSCode扩展API使用符合最新版本规范，向后兼容至1.74.0
- 打包后的.vsix文件通过vsce验证，无安全警告

**输出要求**：
- **问题分类**：阻塞性（编译失败、运行时崩溃）、关键（功能缺失、数据丢失）、重要（性能问题、用户体验）、一般（代码风格、优化建议）
- **修复实现**：提供完整的TypeScript代码，包含类型声明、错误处理、注释说明
- **验证结果**：执行npm run compile和npm run lint的成功输出
- **修复报告**：详细说明修复的具体问题、采用的解决方案、性能和安全改进点
- **禁止内容**：不得包含任何模拟、示例、简化、占位符性质的代码，所有实现必须是生产环境可用的完整代码

**执行约束**：
- 必须保持现有功能的完整性，不得删除或简化核心功能
- 所有修改必须向后兼容，不破坏现有的用户配置
- 代码风格必须符合项目现有的ESLint和Prettier配置
- 修复后的代码必须通过TypeScript编译和单元测试（如存在）
