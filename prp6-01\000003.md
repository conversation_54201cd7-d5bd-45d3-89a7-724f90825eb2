**React/TypeScript前端项目多维度代码审查与修复执行指令**

基于当前临时邮箱客户端项目（React 18 + TypeScript + Vite + Zustand），执行全方位的多维度代码审查和修复，确保代码质量达到生产发布标准。

**1. 多维度代码审查框架**

**1.1 架构完整性审查（空间思维）**
- **模块依赖关系验证**：
  - 扫描src/目录下所有.ts/.tsx文件，验证import语句路径的准确性
  - 检查@/别名路径解析是否正确配置（tsconfig.json和vite.config.ts）
  - 验证懒加载组件（lazy imports）的路径和组件导出正确性
  - 检查Zustand store、React hooks、工具函数的循环依赖问题
- **代码分层架构验证**：
  - API层：services/目录下的API客户端和数据获取逻辑
  - 业务层：store/目录下的状态管理和业务逻辑
  - 展示层：components/目录下的UI组件和交互逻辑
  - 工具层：utils/目录下的通用工具函数
- **横切关注点一致性**：
  - 错误处理：ErrorBoundary、createSafeError、try-catch模式统一性
  - 日志系统：logger实例化和调用方式的一致性
  - 类型定义：types/目录下接口定义的完整性和一致性

**1.2 功能完整性审查（立体思维）**
- **完整调用链路验证**：
  - 用户操作 → React事件处理 → Zustand状态更新 → API调用 → UI反馈的完整流程
  - 邮箱生成：EmailGenerator → createEmailAddress → API调用 → 状态更新 → EmailDisplay
  - 邮件刷新：刷新按钮 → refreshMessages → API调用 → 消息列表更新
  - 验证码提取：邮件接收 → 自动解析 → VerificationCodeExtractor显示
- **API集成完整性**：
  - modernTempEmailAPI中所有方法的实现完整性
  - API响应格式与TypeScript接口定义的匹配性
  - 错误响应的统一处理和用户友好提示
- **状态管理完整性**：
  - Zustand store的持久化配置正确性
  - 状态订阅和更新的响应性
  - 组件间状态共享的一致性

**1.3 逻辑一致性审查（逆向思维）**
- **用户场景反推验证**：
  - 从"生成邮箱"功能反推：UI交互 → 状态管理 → API调用 → 错误处理的完整性
  - 从"JWT自动登录"功能反推：URL参数解析 → token验证 → 状态恢复的逻辑正确性
  - 从"邮件实时刷新"功能反推：轮询机制 → API调用 → 状态更新 → UI刷新的时序正确性
- **错误场景处理反推**：
  - API超时/网络错误时的重试机制和用户提示
  - JWT过期时的状态清理和重新认证流程
  - 本地存储数据损坏时的恢复机制
- **安全性要求反推**：
  - XSS防护：DOMPurify的正确使用和HTML内容净化
  - JWT安全：token存储、传输、过期处理的安全性
  - 域名验证：CORS配置和允许域名的严格验证

**2. React/TypeScript特定代码质量检查**

**2.1 TypeScript类型安全检查**
- **类型定义完整性**：
  - 所有API响应接口的完整定义（EmailMessage、EmailAddress、VerificationCode等）
  - React组件Props接口的完整性和准确性
  - Zustand store状态接口的类型安全性
  - 工具函数参数和返回值的类型注解
- **类型使用正确性**：
  - 消除所有any类型使用，使用具体类型或泛型
  - 确保类型断言（as）的安全性和必要性
  - 验证可选属性（?）和联合类型的正确使用

**2.2 React最佳实践检查**
- **组件设计规范**：
  - 函数组件的memo优化和依赖数组正确性
  - useEffect、useCallback、useMemo的依赖数组完整性
  - 自定义hooks的设计和复用性
- **性能优化验证**：
  - 懒加载组件的正确实现和fallback处理
  - 代码分割策略的有效性（vite.config.ts中的manualChunks）
  - 不必要的重渲染检查和优化

**2.3 前端特定安全检查**
- **XSS防护验证**：
  - 邮件内容显示时的HTML净化处理
  - 用户输入的验证和转义
  - dangerouslySetInnerHTML的安全使用
- **数据验证完整性**：
  - API响应数据的验证和类型检查
  - 表单输入的客户端验证
  - 本地存储数据的完整性验证

**3. 强制修复要求**

**3.1 消除所有模拟和测试数据**
- 移除所有硬编码的示例邮件、验证码数据
- 确保所有数据来源于真实API调用
- 删除开发环境的mock数据和占位符

**3.2 完善错误处理覆盖**
- 每个async/await函数必须包含完整的try-catch
- API调用失败时的用户友好错误提示
- 网络错误、超时、认证失败的分类处理

**3.3 确保生产环境就绪**
- 环境变量配置的完整性和安全性
- 构建配置的优化和安全性
- 部署配置的正确性（netlify.toml、_redirects）

**4. 验证标准**
- TypeScript编译通过，无类型错误
- ESLint检查通过，无代码风格违规
- 所有功能基于真实API，无模拟数据
- 错误处理覆盖率100%
- 生产构建成功，无安全警告

立即开始执行多维度代码审查，按照上述标准进行全面检查和修复。