# 网易云音乐API现代化重构设计文档

## 概述

本设计文档基于需求分析，采用现代化技术栈对网易云音乐API进行完整重构。设计目标是在保持100%功能兼容的前提下，提升代码质量、性能和可维护性。

## 技术栈选择

### 核心框架
- **运行时**: Node.js 18+ (LTS)
- **语言**: TypeScript 5.x
- **Web框架**: Fastify 4.x (替代Express，提供更好的性能和类型支持)
- **包管理**: pnpm (更快的安装速度和更好的依赖管理)

### 开发工具链
- **构建工具**: Vite (快速构建和热重载)
- **代码质量**: ESLint + Prettier + <PERSON><PERSON>
- **测试框架**: Vitest (与Vite集成的测试框架)
- **文档生成**: @fastify/swagger (自动生成OpenAPI文档)

### 依赖库升级
- **HTTP客户端**: axios → undici (Node.js官方推荐)
- **加密库**: crypto-js → Node.js内置crypto模块
- **文件上传**: express-fileupload → @fastify/multipart
- **缓存**: apicache → @fastify/caching
- **环境变量**: dotenv → 内置支持

## 架构设计

### 整体架构

```mermaid
graph TB
    A[Client Request] --> B[Fastify Server]
    B --> C[Authentication Middleware]
    C --> D[Rate Limiting Middleware]
    D --> E[Request Validation]
    E --> F[Route Handler]
    F --> G[Service Layer]
    G --> H[Crypto Service]
    G --> I[Request Service]
    G --> J[Cache Service]
    I --> K[NetEase API]
    J --> L[Redis/Memory Cache]
    H --> M[Encryption Utils]
    F --> N[Response Formatter]
    N --> O[Client Response]
```

### 项目结构

重构项目将在独立的目录 `netease-api-modern/` 中实现，与原项目完全分离：

```
netease-api-modern/
├── src/
│   ├── controllers/          # 控制器层
│   │   ├── auth/            # 认证相关控制器
│   │   ├── music/           # 音乐相关控制器
│   │   ├── user/            # 用户相关控制器
│   │   └── index.ts         # 控制器注册
│   ├── services/            # 服务层
│   │   ├── crypto.service.ts    # 加密服务
│   │   ├── request.service.ts   # 请求服务
│   │   ├── cache.service.ts     # 缓存服务
│   │   └── config.service.ts    # 配置服务
│   ├── middleware/          # 中间件
│   │   ├── auth.middleware.ts   # 认证中间件
│   │   ├── cors.middleware.ts   # CORS中间件
│   │   ├── error.middleware.ts  # 错误处理中间件
│   │   └── rate-limit.middleware.ts # 限流中间件
│   ├── types/               # 类型定义
│   │   ├── api.types.ts     # API类型
│   │   ├── config.types.ts  # 配置类型
│   │   └── common.types.ts  # 通用类型
│   ├── utils/               # 工具函数
│   │   ├── crypto.utils.ts  # 加密工具
│   │   ├── cookie.utils.ts  # Cookie工具
│   │   └── validation.utils.ts # 验证工具
│   ├── config/              # 配置文件
│   │   ├── app.config.ts    # 应用配置
│   │   ├── api.config.ts    # API配置
│   │   └── env.config.ts    # 环境配置
│   ├── plugins/             # Fastify插件
│   │   ├── swagger.plugin.ts    # 文档插件
│   │   ├── cors.plugin.ts       # CORS插件
│   │   └── multipart.plugin.ts  # 文件上传插件
│   └── app.ts               # 应用入口
├── tests/                   # 测试文件
│   ├── unit/               # 单元测试
│   ├── integration/        # 集成测试
│   └── e2e/               # 端到端测试
├── docs/                   # 文档
├── scripts/                # 构建和部署脚本
├── docker/                 # Docker配置
├── package.json
├── tsconfig.json
├── vite.config.ts
├── .eslintrc.js
├── .prettierrc
├── README.md
└── Dockerfile
```

## 组件设计

### 1. 服务层设计

#### CryptoService
负责所有加密相关操作，包括weapi、eapi、linuxapi等加密方式。

```typescript
interface CryptoService {
  weapi(data: Record<string, any>): EncryptedData
  eapi(url: string, data: Record<string, any>): EncryptedData
  linuxapi(data: Record<string, any>): EncryptedData
  decrypt(cipher: string): string
  generateSecretKey(): string
}
```

#### RequestService
统一处理所有对网易云API的请求，支持不同的加密方式和代理配置。

```typescript
interface RequestService {
  request(uri: string, data: any, options: RequestOptions): Promise<ApiResponse>
  setDefaultOptions(options: Partial<RequestOptions>): void
  createRequestWithAuth(cookie: string): (uri: string, data: any) => Promise<ApiResponse>
}
```

#### CacheService
提供灵活的缓存策略，支持内存缓存和Redis缓存。

```typescript
interface CacheService {
  get<T>(key: string): Promise<T | null>
  set<T>(key: string, value: T, ttl?: number): Promise<void>
  del(key: string): Promise<void>
  clear(): Promise<void>
}
```

### 2. 控制器层设计

每个控制器负责特定领域的API端点，采用装饰器模式进行路由注册。

```typescript
@Controller('/api')
export class MusicController {
  constructor(
    private requestService: RequestService,
    private cacheService: CacheService
  ) {}

  @Get('/search')
  @Cache(120) // 缓存2分钟
  async search(@Query() query: SearchQuery): Promise<SearchResponse> {
    // 实现搜索逻辑
  }

  @Get('/song/url')
  async getSongUrl(@Query() query: SongUrlQuery): Promise<SongUrlResponse> {
    // 实现获取歌曲URL逻辑
  }
}
```

### 3. 中间件设计

#### 认证中间件
处理Cookie解析和用户认证状态验证。

```typescript
export const authMiddleware = async (request: FastifyRequest, reply: FastifyReply) => {
  const cookie = parseCookie(request.headers.cookie)
  request.user = await validateUser(cookie)
  request.authCookie = cookie
}
```

#### 错误处理中间件
统一处理所有错误，确保错误响应格式一致。

```typescript
export const errorHandler = (error: Error, request: FastifyRequest, reply: FastifyReply) => {
  const statusCode = getStatusCode(error)
  const errorResponse = formatError(error, statusCode)
  reply.status(statusCode).send(errorResponse)
}
```

## 数据模型

### API响应格式
保持与原版完全一致的响应格式。

```typescript
interface ApiResponse<T = any> {
  code: number
  message?: string
  data?: T
  cookie?: string[]
}
```

### 配置模型
```typescript
interface AppConfig {
  server: {
    port: number
    host: string
    cors: CorsOptions
  }
  api: {
    domain: string
    timeout: number
    retries: number
  }
  cache: {
    type: 'memory' | 'redis'
    ttl: number
    redis?: RedisOptions
  }
  security: {
    rateLimit: RateLimitOptions
    allowedOrigins: string[]
  }
}
```

### 加密配置
```typescript
interface CryptoConfig {
  defaultMethod: 'weapi' | 'eapi' | 'linuxapi' | 'api'
  keys: {
    weapi: string
    eapi: string
    linuxapi: string
  }
  encryptResponse: boolean
}
```

## 错误处理策略

### 错误分类
1. **网络错误**: 连接超时、DNS解析失败等
2. **API错误**: 网易云API返回的业务错误
3. **验证错误**: 请求参数验证失败
4. **系统错误**: 服务器内部错误

### 错误处理流程
```mermaid
graph TD
    A[Error Occurred] --> B{Error Type}
    B -->|Network Error| C[Retry Logic]
    B -->|API Error| D[Format API Error]
    B -->|Validation Error| E[Format Validation Error]
    B -->|System Error| F[Log Error & Return 500]
    C --> G{Retry Success?}
    G -->|Yes| H[Return Success]
    G -->|No| I[Return Network Error]
    D --> J[Return Formatted Error]
    E --> J
    F --> J
    I --> J
    J --> K[Send Error Response]
```

## 测试策略

### 单元测试
- 服务层函数测试
- 工具函数测试
- 中间件测试

### 集成测试
- API端点测试
- 数据库集成测试
- 缓存集成测试

### 端到端测试
- 完整API流程测试
- 认证流程测试
- 错误场景测试

### 测试覆盖率目标
- 代码覆盖率: ≥90%
- 分支覆盖率: ≥85%
- 函数覆盖率: ≥95%

## 性能优化

### 1. 请求优化
- 使用连接池复用HTTP连接
- 实现智能重试机制
- 支持请求并发控制

### 2. 缓存策略
- API响应缓存（可配置TTL）
- 用户认证信息缓存
- 静态资源缓存

### 3. 内存优化
- 使用流式处理大文件
- 及时释放不需要的对象
- 监控内存使用情况

## 安全考虑

### 1. 输入验证
- 严格的参数类型检查
- SQL注入防护
- XSS攻击防护

### 2. 认证安全
- 安全的Cookie处理
- 防止CSRF攻击
- 会话管理安全

### 3. 网络安全
- HTTPS强制使用
- 安全的代理配置
- 请求来源验证

## 部署策略

### 1. 容器化部署
```dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN pnpm install --frozen-lockfile
COPY . .
RUN pnpm build
EXPOSE 3000
CMD ["pnpm", "start"]
```

### 2. Serverless部署
支持Vercel、Netlify等平台的Serverless部署。

### 3. 传统服务器部署
提供PM2配置和系统服务配置。

## 监控和日志

### 日志策略
- 结构化日志记录
- 不同级别的日志分离
- 敏感信息脱敏

### 监控指标
- API响应时间
- 错误率统计
- 内存和CPU使用率
- 缓存命中率

## 迁移计划

### 阶段1: 核心框架迁移
- 搭建Fastify基础框架
- 实现基础中间件
- 配置开发环境

### 阶段2: 服务层重构
- 实现CryptoService
- 实现RequestService
- 实现CacheService

### 阶段3: API端点迁移
- 按模块逐步迁移API端点
- 保证每个端点的功能一致性
- 添加相应的测试

### 阶段4: 优化和完善
- 性能优化
- 安全加固
- 文档完善

这个设计确保了在使用现代技术栈的同时，完全保持与原版API的功能兼容性，为用户提供无缝的迁移体验。