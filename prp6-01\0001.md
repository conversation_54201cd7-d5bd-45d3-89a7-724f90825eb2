从多维度角度出发进行代码及核查，分别以空间思维、立体思维、逆向思维的方式方法包括且不限于上述方法，对本项目从单文件代码到多文件交叉以及项目整体逻辑、方式、方法、调用、关系等进行全方位检查修复检查发现的问题、错误、异常和不完整，去除所有猜测性质代码、数据，模拟数据等全部以真实实际数据为准进行核对校验

使用自动执行模式，对项目进行全方位的多维度代码审查和修复：

**审查维度要求**：
1. **空间思维** - 分析代码在文件系统中的组织结构、模块间依赖关系、配置文件路径的正确性
2. **立体思维** - 检查前端-后端-数据库的完整调用链路、API接口的一致性、数据流向的完整性
3. **逆向思维** - 从用户操作结果反推代码逻辑、从错误日志追溯根本原因、验证异常处理的完备性

**检查范围**：
- **单文件级别**：语法错误、逻辑漏洞、未使用变量、导入问题、函数完整性
- **多文件交叉**：模块间调用关系、API路由匹配、配置文件引用、静态资源路径
- **项目整体**：架构一致性、数据流完整性、错误处理链条、安全机制覆盖

**修复要求**：
1. **移除所有模拟数据** - 将生成的假数据替换为真实的系统监控数据获取
2. **完善数据获取** - 确保控数据来源于真实的API调用或日志分析
3. **修复配置路径** - 验证所有文件路径、端口配置、服务地址的准确性
4. **完善错误处理** - 确保每个API调用、文件操作都有完整的异常处理
5. **数据一致性** - 前后端数据格式统一、API响应结构标准化

**验证标准**：
- 所有功能必须基于真实数据源
- 代码逻辑必须完整可执行
- 错误处理必须覆盖所有异常情况
- 配置和路径必须与实际部署环境匹配

**输出要求**：
- 详细列出发现的问题和修复方案
- 确保修复后的代码达到生产环境标准
- 不生成总结文档，专注于代码修复实现